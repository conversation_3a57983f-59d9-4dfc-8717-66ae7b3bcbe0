@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* #region  /**=========== Primary Color =========== */
  /* !STARTERCONF Customize these variable, copy and paste from /styles/colors.css for list of colors */
  --tw-color-primary-50: 240 253 244;
  --tw-color-primary-100: 220 252 231;
  --tw-color-primary-200: 187 247 208;
  --tw-color-primary-300: 134 239 172;
  --tw-color-primary-400: 74 222 128;
  --tw-color-primary-500: 34 197 94;
  --tw-color-primary-600: 22 163 74;
  --tw-color-primary-700: 21 128 61;
  --tw-color-primary-800: 22 101 52;
  --tw-color-primary-900: 20 83 45;
  --color-primary-50: rgb(var(--tw-color-primary-50)); /* #f0fdf4 */
  --color-primary-100: rgb(var(--tw-color-primary-100)); /* #dcfce7 */
  --color-primary-200: rgb(var(--tw-color-primary-200)); /* #bbf7d0 */
  --color-primary-300: rgb(var(--tw-color-primary-300)); /* #86efac */
  --color-primary-400: rgb(var(--tw-color-primary-400)); /* #4ade80 */
  --color-primary-500: rgb(var(--tw-color-primary-500)); /* #22c55e */
  --color-primary-600: rgb(var(--tw-color-primary-600)); /* #16a34a */
  --color-primary-700: rgb(var(--tw-color-primary-700)); /* #15803d */
  --color-primary-800: rgb(var(--tw-color-primary-800)); /* #166534 */
  --color-primary-900: rgb(var(--tw-color-primary-900)); /* #14532d */
  /* #endregion  /**======== Primary Color =========== */
}

@layer base {
  /* inter var - latin */
  @font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 100 900;
    font-display: block;
    src: url('/fonts/inter-var-latin.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
      U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212,
      U+2215, U+FEFF, U+FFFD;
  }

  .cursor-newtab {
    cursor: url('/images/new-tab.png') 10 10, pointer;
  }

  /* #region  /**=========== Typography =========== */
  .h0 {
    @apply font-primary text-3xl font-bold md:text-5xl;
  }

  h1,
  .h1 {
    @apply font-primary text-2xl font-bold md:text-4xl;
  }

  h2,
  .h2 {
    @apply font-primary text-xl font-bold md:text-3xl;
  }

  h3,
  .h3 {
    @apply font-primary text-lg font-bold md:text-2xl;
  }

  h4,
  .h4 {
    @apply font-primary text-base font-bold md:text-lg;
  }

  body,
  .p {
    @apply font-primary text-sm md:text-base;
  }
  /* #endregion  /**======== Typography =========== */

  .layout {
    /* 1100px */
    max-width: 68.75rem;
    @apply mx-auto w-11/12;
  }

  .bg-dark a.custom-link {
    @apply border-gray-200 hover:border-gray-200/0;
  }

  /* Class to adjust with sticky footer */
  .min-h-main {
    @apply min-h-[calc(100vh-56px)];
  }
}

@layer utilities {
  .animated-underline {
    background-image: linear-gradient(#33333300, #33333300),
      linear-gradient(
        to right,
        var(--color-primary-400),
        var(--color-primary-500)
      );
    background-size: 100% 2px, 0 2px;
    background-position: 100% 100%, 0 100%;
    background-repeat: no-repeat;
  }
  @media (prefers-reduced-motion: no-preference) {
    .animated-underline {
      transition: 0.3s ease;
      transition-property: background-size, color, background-color,
        border-color;
    }
  }
  .animated-underline:hover,
  .animated-underline:focus-visible {
    background-size: 0 2px, 100% 2px;
  }
}
