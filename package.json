{"name": "moontv", "version": "0.1.0", "private": true, "scripts": {"dev": "pnpm gen:runtime && next dev -H 0.0.0.0", "build": "pnpm gen:runtime && next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint src --fix && pnpm format", "lint:strict": "eslint --max-warnings=0 src", "typecheck": "tsc --noEmit --incremental false", "test:watch": "jest --watch", "test": "jest", "format": "prettier -w .", "format:check": "prettier -c .", "gen:runtime": "node scripts/convert-config.js", "postbuild": "next-sitemap --config next-sitemap.config.js", "prepare": "husky install", "pages:build": "pnpm gen:runtime && next build && next-sitemap --config next-sitemap.config.js && npx @cloudflare/next-on-pages --experimental-minify"}, "dependencies": {"@cloudflare/next-on-pages": "^1.13.12", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@upstash/redis": "^1.25.0", "@vidstack/react": "^1.12.13", "clsx": "^2.0.0", "framer-motion": "^12.18.1", "hls.js": "^1.6.5", "lucide-react": "^0.438.0", "media-icons": "^1.1.5", "next": "^14.2.23", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "node-cron": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.4.0", "redis": "^4.6.7", "swiper": "^11.2.8", "tailwind-merge": "^2.6.0", "vidstack": "^0.6.15", "zod": "^3.24.1"}, "devDependencies": {"@commitlint/cli": "^16.3.0", "@commitlint/config-conventional": "^16.2.4", "@svgr/webpack": "^8.1.0", "@tailwindcss/forms": "^0.5.10", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^15.0.7", "@types/node": "24.0.3", "@types/react": "^18.3.18", "@types/testing-library__jest-dom": "^5.14.9", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "^14.2.23", "eslint-config-prettier": "^8.10.0", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^7.0.4", "jest": "^27.5.1", "lint-staged": "^12.5.0", "next-router-mock": "^0.9.0", "next-sitemap": "^2.5.28", "postcss": "^8.5.1", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.5.0", "tailwindcss": "^3.4.17", "typescript": "^4.9.5"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["eslint --max-warnings=0", "prettier -w"], "**/*.{json,css,scss,md,webmanifest}": ["prettier -w"]}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}